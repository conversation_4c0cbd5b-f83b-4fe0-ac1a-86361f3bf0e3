/**
 * 测试会话功能修复
 * 验证会话创建、消息发送和查询功能
 */

const BASE_URL = 'http://localhost:3001';

async function testSessionFunctionality() {
  console.log('🧪 开始测试会话功能...\n');

  try {
    // 1. 创建新会话
    console.log('1️⃣ 创建新会话...');
    const createResponse = await fetch(`${BASE_URL}/api/sessions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sessionType: 'chat',
        title: '测试会话',
        user_id: 'test-user-123'
      })
    });

    if (!createResponse.ok) {
      throw new Error(`创建会话失败: ${createResponse.status}`);
    }

    const createResult = await createResponse.json();
    const threadId = createResult.session.thread_id;
    console.log(`✅ 会话创建成功: ${threadId}\n`);

    // 2. 发送消息到会话
    console.log('2️⃣ 发送测试消息...');
    const chatResponse = await fetch(`${BASE_URL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          { role: 'user', content: '你好，这是一个测试消息' }
        ],
        thread_id: threadId,
        user_id: 'test-user-123'
      })
    });

    if (!chatResponse.ok) {
      throw new Error(`发送消息失败: ${chatResponse.status}`);
    }

    console.log('✅ 消息发送成功\n');

    // 等待一下让消息处理完成
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 3. 查询会话信息
    console.log('3️⃣ 查询会话信息...');
    const sessionResponse = await fetch(`${BASE_URL}/api/sessions?thread_id=${threadId}`);
    
    if (!sessionResponse.ok) {
      throw new Error(`查询会话失败: ${sessionResponse.status}`);
    }

    const sessionInfo = await sessionResponse.json();
    console.log('📋 会话信息:', {
      thread_id: sessionInfo.thread_id,
      title: sessionInfo.title,
      message_count: sessionInfo.message_count,
      status: sessionInfo.status,
      created_at: sessionInfo.created_at,
      last_accessed: sessionInfo.last_accessed
    });

    // 4. 查询会话列表
    console.log('\n4️⃣ 查询会话列表...');
    const listResponse = await fetch(`${BASE_URL}/api/sessions?action=list&user_id=test-user-123`);
    
    if (!listResponse.ok) {
      throw new Error(`查询会话列表失败: ${listResponse.status}`);
    }

    const listResult = await listResponse.json();
    console.log('📝 会话列表:', {
      total: listResult.total,
      sessions_count: listResult.sessions.length,
      first_session: listResult.sessions[0] ? {
        thread_id: listResult.sessions[0].thread_id,
        title: listResult.sessions[0].title,
        message_count: listResult.sessions[0].message_count
      } : null
    });

    // 5. 查询会话统计
    console.log('\n5️⃣ 查询会话统计...');
    const statsResponse = await fetch(`${BASE_URL}/api/sessions?action=stats&user_id=test-user-123`);
    
    if (!statsResponse.ok) {
      throw new Error(`查询统计失败: ${statsResponse.status}`);
    }

    const stats = await statsResponse.json();
    console.log('📊 会话统计:', {
      total_sessions: stats.total_sessions,
      active_sessions: stats.active_sessions,
      total_messages: stats.total_messages,
      avg_messages_per_session: stats.avg_messages_per_session
    });

    // 验证结果
    console.log('\n🔍 验证结果...');
    
    if (sessionInfo.message_count > 0) {
      console.log('✅ 会话消息计数正常');
    } else {
      console.log('❌ 会话消息计数异常');
    }

    if (listResult.total > 0) {
      console.log('✅ 会话列表查询正常');
    } else {
      console.log('❌ 会话列表查询异常');
    }

    if (stats.total_sessions > 0) {
      console.log('✅ 会话统计正常');
    } else {
      console.log('❌ 会话统计异常');
    }

    console.log('\n🎉 测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error);
  }
}

// 运行测试
testSessionFunctionality();
