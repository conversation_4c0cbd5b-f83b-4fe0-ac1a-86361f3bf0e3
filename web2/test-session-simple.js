/**
 * 简化的会话功能测试
 * 只测试会话创建和查询功能
 */

const BASE_URL = 'http://localhost:3001';

async function testSessionBasics() {
  console.log('🧪 开始测试基础会话功能...\n');

  try {
    // 1. 创建新会话
    console.log('1️⃣ 创建新会话...');
    const createResponse = await fetch(`${BASE_URL}/api/sessions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sessionType: 'chat',
        title: '测试会话',
        user_id: 'test-user-456'
      })
    });

    if (!createResponse.ok) {
      throw new Error(`创建会话失败: ${createResponse.status}`);
    }

    const createResult = await createResponse.json();
    const threadId = createResult.session.thread_id;
    console.log(`✅ 会话创建成功: ${threadId}`);
    console.log('📋 创建的会话信息:', {
      thread_id: createResult.session.thread_id,
      title: createResult.session.title,
      session_type: createResult.session.session_type,
      status: createResult.session.status,
      message_count: createResult.session.message_count
    });

    // 2. 立即查询刚创建的会话
    console.log('\n2️⃣ 查询刚创建的会话...');
    const sessionResponse = await fetch(`${BASE_URL}/api/sessions?thread_id=${threadId}`);
    
    if (!sessionResponse.ok) {
      throw new Error(`查询会话失败: ${sessionResponse.status}`);
    }

    const sessionInfo = await sessionResponse.json();
    console.log('✅ 会话查询成功');
    console.log('📋 查询到的会话信息:', {
      thread_id: sessionInfo.thread_id,
      title: sessionInfo.title,
      message_count: sessionInfo.message_count,
      status: sessionInfo.status,
      created_at: sessionInfo.created_at,
      last_accessed: sessionInfo.last_accessed
    });

    // 3. 查询会话列表
    console.log('\n3️⃣ 查询会话列表...');
    const listResponse = await fetch(`${BASE_URL}/api/sessions?action=list&user_id=test-user-456`);
    
    if (!listResponse.ok) {
      throw new Error(`查询会话列表失败: ${listResponse.status}`);
    }

    const listResult = await listResponse.json();
    console.log('✅ 会话列表查询成功');
    console.log('📝 会话列表信息:', {
      total: listResult.total,
      sessions_count: listResult.sessions.length,
      page: listResult.page,
      limit: listResult.limit
    });

    if (listResult.sessions.length > 0) {
      console.log('📋 第一个会话:', {
        thread_id: listResult.sessions[0].thread_id,
        title: listResult.sessions[0].title,
        message_count: listResult.sessions[0].message_count,
        status: listResult.sessions[0].status
      });
    }

    // 4. 查询会话统计
    console.log('\n4️⃣ 查询会话统计...');
    const statsResponse = await fetch(`${BASE_URL}/api/sessions?action=stats&user_id=test-user-456`);
    
    if (!statsResponse.ok) {
      throw new Error(`查询统计失败: ${statsResponse.status}`);
    }

    const stats = await statsResponse.json();
    console.log('✅ 会话统计查询成功');
    console.log('📊 会话统计:', {
      total_sessions: stats.total_sessions,
      active_sessions: stats.active_sessions,
      total_messages: stats.total_messages,
      avg_messages_per_session: stats.avg_messages_per_session,
      session_types: stats.session_types
    });

    // 5. 验证结果
    console.log('\n🔍 验证结果...');
    
    let allTestsPassed = true;

    if (sessionInfo.thread_id === threadId) {
      console.log('✅ 会话ID匹配正确');
    } else {
      console.log('❌ 会话ID不匹配');
      allTestsPassed = false;
    }

    if (listResult.total > 0) {
      console.log('✅ 会话列表查询正常');
    } else {
      console.log('❌ 会话列表查询异常 - 没有找到会话');
      allTestsPassed = false;
    }

    if (stats.total_sessions > 0) {
      console.log('✅ 会话统计正常');
    } else {
      console.log('❌ 会话统计异常 - 统计数据为0');
      allTestsPassed = false;
    }

    // 检查会话是否能在列表中找到
    const foundInList = listResult.sessions.some(s => s.thread_id === threadId);
    if (foundInList) {
      console.log('✅ 新创建的会话能在列表中找到');
    } else {
      console.log('❌ 新创建的会话在列表中找不到');
      allTestsPassed = false;
    }

    if (allTestsPassed) {
      console.log('\n🎉 所有基础测试通过！会话功能正常工作。');
    } else {
      console.log('\n⚠️  部分测试失败，需要进一步检查。');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error);
  }
}

// 运行测试
testSessionBasics();
